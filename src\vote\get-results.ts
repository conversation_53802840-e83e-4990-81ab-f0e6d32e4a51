import { Middleware } from "@koa/router";
import { sc } from "../services";
import { OptionEntity } from "../table-definitions";

export interface ResultItem {
  name: string;
  voteCount: number;
}

export const getResultsMiddleware: Middleware = async ({ response, query }) => {
  try {
    // Scan all options from the option table
    const optionsResponse = await sc.db.query({
      TableName: sc.vars.optionTableName,
      KeyConditionExpression: "pk = :pk",
      ExpressionAttributeValues: {
        ":pk": query.eventId,
      },
    });

    if (!optionsResponse || !optionsResponse.Items) {
      response.status = 200;
      response.body = [];
      return;
    }

    // Transform the options to return only name and voteCount
    const results: ResultItem[] = optionsResponse.Items.map((item) => {
      const option = item as OptionEntity;
      return {
        optionId: option.sk,
        name: option.name,
        voteCount: option.voteCount,
      };
    });

    response.status = 200;
    response.body = results;
  } catch (error) {
    console.log(JSON.stringify(error));
    response.status = 500;
    response.body = "Error interno del servidor";
  }
};
