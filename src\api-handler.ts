import Koa from "koa";
import Router from "@koa/router";
import { bodyParser } from "@koa/bodyparser";
import serverlessExpress from "@codegenie/serverless-express";
import { voteRouter } from "./vote";

const koa = new Koa();
const router = new Router({
  prefix: "/live",
});

/**
 * Middlewares
 */
koa.use(bodyParser());

/**
 * Setup routes
 */
router.use("/event-polling", voteRouter.routes());

/**
 * Finish setup
 */
koa.use(router.routes());

export const app = koa.callback();
export const handler = serverlessExpress({ app });
