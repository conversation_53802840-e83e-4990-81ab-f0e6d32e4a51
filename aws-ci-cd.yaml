AWSTemplateFormatVersion: "2010-09-09"
Description: Creates CodeBuild and CodePipelines for event-polling service
Parameters:
  EnvName:
    Type: AWS::SSM::Parameter::Value<String>
    Description: The environment name.
    Default: /env/name

  GithubToken:
    Type: AWS::SSM::Parameter::Value<String>
    Description: The github token.
    Default: /codebuild/gh_token

Conditions:
  IsDev: !Equals [!Ref EnvName, "dev"]
  IsStg: !Equals [!Ref EnvName, "stg"]
  IsProd: !Equals [!Ref EnvName, "prod"]

Mappings:
  BranchMap:
    dev:
      Name: dev
    stg:
      Name: stg
    prod:
      Name: main

Resources:
  # Create the CodeBuild project to build the source code
  CodeBuildProject:
    Type: AWS::CodeBuild::Project
    Properties:
      Name: event-polling-build
      Description: Build the event-polling project
      EncryptionKey: !ImportValue CI-CD-KMSKeyArn
      ServiceRole: !ImportValue CI-CD-CodeBuildServiceRoleArn
      Source:
        Type: CODEPIPELINE
        BuildSpec: aws-buildspec.yaml
      Artifacts:
        Type: CODEPIPELINE
      Environment:
        Type: LINUX_CONTAINER
        ComputeType: BUILD_GENERAL1_SMALL
        Image: aws/codebuild/standard:7.0
        EnvironmentVariables:
          - Name: ARTIFACT_S3_BUCKET
            Value: !ImportValue CI-CD-ArtifactBucket
          - Name: ARTIFACT_S3_BUCKET_PREFIX
            Value: event-polling/builds
          - Name: GITHUB_TOKEN
            Value: !Ref GithubToken
      TimeoutInMinutes: 5

  Pipeline:
    Type: AWS::CodePipeline::Pipeline
    Properties:
      Name: event-polling
      RoleArn: !ImportValue CI-CD-CodePipelineServiceRoleArn
      ArtifactStore:
        Type: S3
        Location: !ImportValue CI-CD-ArtifactBucket
        EncryptionKey:
          Id: !ImportValue CI-CD-ArtifactBucketKMSKeyId
          Type: KMS
      Stages:
        - Name: FetchSource
          Actions:
            - Name: GitHub
              ActionTypeId:
                Category: Source
                Owner: AWS
                Version: 1
                Provider: CodeStarSourceConnection
              Configuration:
                ConnectionArn: !ImportValue CI-CD-GithubConnection
                FullRepositoryId: Mainframe-Peru/event-polling
                BranchName: !FindInMap [BranchMap, !Ref EnvName, Name]
              OutputArtifacts:
                - Name: Source
              RunOrder: 1

        - Name: Build
          Actions:
            - Name: CodeBuild
              ActionTypeId:
                Category: Build
                Owner: AWS
                Version: 1
                Provider: CodeBuild
              InputArtifacts:
                - Name: Source
              OutputArtifacts:
                - Name: BuildOutput
              Configuration:
                ProjectName: !Ref CodeBuildProject

        - Name: Deploy
          Actions:
            - Name: CreateChangeSet
              InputArtifacts:
                - Name: BuildOutput
              ActionTypeId:
                Category: Deploy
                Owner: AWS
                Version: 1
                Provider: CloudFormation
              Configuration:
                StackName: event-polling
                ChangeSetName: event-polling-changeset
                ActionMode: CHANGE_SET_REPLACE
                Capabilities: CAPABILITY_NAMED_IAM
                TemplatePath: BuildOutput::aws-sam-template-final.yaml
                RoleArn: !ImportValue CI-CD-CodePipelineServiceRoleArn
              RunOrder: 1
              RoleArn: !ImportValue CI-CD-CodePipelineServiceRoleArn

            - !If
              - IsProd
              - Name: ApproveProdDeployment
                RunOrder: 2
                ActionTypeId:
                  Category: Approval
                  Owner: AWS
                  Version: 1
                  Provider: Manual

              - !Ref AWS::NoValue

            - Name: ExecuteChangeSet
              ActionTypeId:
                Category: Deploy
                Owner: AWS
                Version: 1
                Provider: CloudFormation
              Configuration:
                StackName: event-polling
                ChangeSetName: event-polling-changeset
                ActionMode: CHANGE_SET_EXECUTE
                RoleArn: !ImportValue CI-CD-CodePipelineServiceRoleArn
              InputArtifacts:
                - Name: BuildOutput
              RunOrder: 3
              RoleArn: !ImportValue CI-CD-CodePipelineServiceRoleArn
