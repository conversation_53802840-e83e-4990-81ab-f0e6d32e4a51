import { Middleware } from "@koa/router";
import { sc } from "../services";
import * as z from "zod";
import { CodeEntity, VoteEntity } from "../table-definitions";

export const RegisterVoteRequestSchema = z.object({
  eventId: z.string(),
  email: z.string(),
  optionId: z.string(),
  code: z.string(),
});

export const registerVoteMiddleware: Middleware = async ({
  request,
  response,
}) => {
  try {
    const body = RegisterVoteRequestSchema.parse(request.body);

    //Validates code exists or hasn't been used
    const codeResponse = await sc.db.get({
      TableName: sc.vars.codeTableName,
      Key: {
        pk: body.eventId,
        sk: body.code,
      },
    });

    if (!codeResponse || !codeResponse.Item) {
      response.status = 400;
      response.body = "Código inválido";
      return;
    }

    const code = codeResponse.Item as CodeEntity;

    if (code.used) {
      response.status = 400;
      response.body = "El código ha sido usado";
      return;
    }

    // Validates the option exists
    const optionResponse = await sc.db.get({
      TableName: sc.vars.optionTableName,
      Key: {
        pk: body.eventId,
        sk: body.optionId,
      },
    });

    if (!optionResponse || !optionResponse.Item) {
      response.status = 400;
      response.body = "La opción no existe";
      return;
    }

    // Validates user hasn't voted previously
    const userResponse = await sc.db.query({
      TableName: sc.vars.voteTableName,
      IndexName: "GSIEmailEvent",
      KeyConditionExpression: "email = :email AND eventId = :eventId",
      ExpressionAttributeValues: {
        ":email": body.email,
        ":eventId": body.eventId,
      },
    });

    if (userResponse && userResponse.Items && userResponse.Items.length > 0) {
      response.status = 400;
      response.body = "El usuario ha votado previamente";
      return;
    }

    // Registers vote
    const voteEntity: VoteEntity = {
      pk: `${body.eventId}#${body.optionId}`,
      sk: new Date().getTime(),
      eventId: body.eventId,
      email: body.email,
      code: body.code,
    };

    await sc.db.put({
      TableName: sc.vars.voteTableName,
      Item: voteEntity,
    });

    // Mark code as used
    await sc.db.update({
      TableName: sc.vars.codeTableName,
      Key: {
        pk: body.eventId,
        sk: body.code,
      },
      UpdateExpression: "SET #used = :used",
      ExpressionAttributeNames: {
        "#used": "used",
      },
      ExpressionAttributeValues: {
        ":used": true,
      },
    });

    // Increment vote count for the option
    await sc.db.update({
      TableName: sc.vars.optionTableName,
      Key: {
        pk: body.eventId,
        sk: body.optionId,
      },
      UpdateExpression: "ADD voteCount :increment",
      ExpressionAttributeValues: {
        ":increment": 1,
      },
    });
  } catch (error) {
    console.log(JSON.stringify(error));
  }
  response.body = null;
};
