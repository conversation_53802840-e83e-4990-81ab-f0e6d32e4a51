import { app } from "../src/api-handler";
import request from "supertest";
import { sc } from "../src/services";
import { CodeEntity, OptionEntity } from "../src/table-definitions";

describe("register vote tests", () => {
  const testEvent = "test-event-123";
  const testOption = "test-option-456";
  const testEmail = "<EMAIL>";
  const testCode = "ABC123";

  beforeEach(async () => {
    // Clean up any existing test data
    await cleanupTestData();
  });

  afterEach(async () => {
    // Clean up test data after each test
    await cleanupTestData();
  });

  const cleanupTestData = async () => {
    try {
      // Clean up vote table - scan and delete all votes for test event
      const voteQuery = await sc.db.scan({
        TableName: sc.vars.voteTableName,
      });

      if (voteQuery.Items) {
        for (const item of voteQuery.Items) {
          await sc.db.delete({
            TableName: sc.vars.voteTableName,
            Key: {
              pk: item.pk,
              sk: item.sk,
            },
          });
        }
      }

      // Clean up code table
      await sc.db.delete({
        TableName: sc.vars.codeTableName,
        Key: {
          pk: testEvent,
          sk: testCode,
        },
      });

      // Clean up option table
      await sc.db.delete({
        TableName: sc.vars.optionTableName,
        Key: {
          pk: testEvent,
          sk: testOption,
        },
      });
    } catch (error) {
      // Ignore errors during cleanup
    }
  };

  const setupValidCode = async () => {
    const codeEntity: CodeEntity = {
      pk: testEvent,
      sk: testCode,
      used: false,
    };

    await sc.db.put({
      TableName: sc.vars.codeTableName,
      Item: codeEntity,
    });
  };

  const setupValidOption = async () => {
    const optionEntity: OptionEntity = {
      pk: testEvent,
      sk: testOption,
      name: "Test Option",
      voteCount: 0,
    };

    await sc.db.put({
      TableName: sc.vars.optionTableName,
      Item: optionEntity,
    });
  };

  test("Successfully register a vote with valid data", async () => {
    // Setup test data
    await setupValidCode();
    await setupValidOption();

    const voteRequest = {
      eventId: testEvent,
      email: testEmail,
      optionId: testOption,
      code: testCode,
    };

    const response = await request(app)
      .post("/live/event-polling/")
      .send(voteRequest);

    expect(response.statusCode).toEqual(204);

    // Verify vote was saved in database
    const voteResponse = await sc.db.scan({
      TableName: sc.vars.voteTableName,
    });

    if(!voteResponse || !voteResponse.Items) {
      fail("No vote found in database");
    }

    expect(voteResponse.Items[0]).toEqual(
      expect.objectContaining({
        pk: `${testEvent}#${testOption}`,
        sk: expect.any(Number),
        email: testEmail,
        code: testCode,
      }),
    );

    // Verify code was marked as used
    const codeResponse = await sc.db.get({
      TableName: sc.vars.codeTableName,
      Key: {
        pk: testEvent,
        sk: testCode,
      },
    });

    expect(codeResponse.Item).toEqual(
      expect.objectContaining({
        pk: testEvent,
        sk: testCode,
        used: true,
      }),
    );

    // Verify option vote count was incremented
    const optionResponse = await sc.db.get({
      TableName: sc.vars.optionTableName,
      Key: {
        pk: testEvent,
        sk: testOption,
      },
    });

    expect(optionResponse.Item).toEqual(
      expect.objectContaining({
        pk: testEvent,
        sk: testOption,
        name: "Test Option",
        voteCount: 1,
      }),
    );
  });

  test("Reject vote with invalid code", async () => {
    await setupValidOption();
    // Don't setup code - it should be invalid

    const voteRequest = {
      eventId: testEvent,
      email: testEmail,
      optionId: testOption,
      code: testCode,
    };

    const response = await request(app)
      .post("/live/event-polling/")
      .send(voteRequest);

    expect(response.statusCode).toEqual(400);
    expect(response.text).toEqual("Código inválido");
  });

  test("Reject vote with already used code", async () => {
    await setupValidOption();

    // Setup used code
    const usedCodeEntity: CodeEntity = {
      pk: testEvent,
      sk: testCode,
      used: true,
    };

    await sc.db.put({
      TableName: sc.vars.codeTableName,
      Item: usedCodeEntity,
    });

    const voteRequest = {
      eventId: testEvent,
      email: testEmail,
      optionId: testOption,
      code: testCode,
    };

    const response = await request(app)
      .post("/live/event-polling/")
      .send(voteRequest);

    expect(response.statusCode).toEqual(400);
    expect(response.text).toEqual("El código ha sido usado");
  });

  test("Reject vote with invalid option", async () => {
    await setupValidCode();
    // Don't setup option - it should be invalid

    const voteRequest = {
      eventId: testEvent,
      email: testEmail,
      optionId: testOption,
      code: testCode,
    };

    const response = await request(app)
      .post("/live/event-polling/")
      .send(voteRequest);

    expect(response.statusCode).toEqual(400);
    expect(response.text).toEqual("La opción no existe");
  });

  test("Increment vote count correctly when option already has votes", async () => {
    await setupValidCode();

    // Setup option with existing vote count
    const optionEntity: OptionEntity = {
      pk: testEvent,
      sk: testOption,
      name: "Test Option",
      voteCount: 5, // Start with 5 votes
    };

    await sc.db.put({
      TableName: sc.vars.optionTableName,
      Item: optionEntity,
    });

    const voteRequest = {
      eventId: testEvent,
      email: testEmail,
      optionId: testOption,
      code: testCode,
    };

    const response = await request(app)
      .post("/live/event-polling/")
      .send(voteRequest);

    expect(response.statusCode).toEqual(204);

    // Verify option vote count was incremented from 5 to 6
    const optionResponse = await sc.db.get({
      TableName: sc.vars.optionTableName,
      Key: {
        pk: testEvent,
        sk: testOption,
      },
    });

    expect(optionResponse.Item).toEqual(
      expect.objectContaining({
        voteCount: 6,
      }),
    );
  });
});
