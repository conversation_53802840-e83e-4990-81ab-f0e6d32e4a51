AWSTemplateFormatVersion: "2010-09-09"
Transform: AWS::Serverless-2016-10-31
Description: MS for event polling. Initially for FILO contest
Parameters:
  ServiceName:
    Type: String
    Default: event-polling

  EnvName:
    Type: AWS::SSM::Parameter::Value<String>
    Description: The environment name.
    Default: /env/name

Conditions:
  IsDev: !Equals [!Ref EnvName, "dev"]
  IsStg: !Equals [!Ref EnvName, "stg"]
  IsProd: !Equals [!Ref EnvName, "prod"]
  PrefixResources: !Or
    - !Equals [!Ref EnvName, "dev"]
    - !Equals [!Ref EnvName, "stg"]

Resources:
  #----------------------------------------------------------------
  # Lambda Role
  #----------------------------------------------------------------
  LambdaFunctionRole:
    Type: AWS::IAM::Role
    Properties:
      RoleName: !Sub ${ServiceName}-lambdafnc-role
      AssumeRolePolicyDocument:
        Version: "2012-10-17"
        Statement:
          - Effect: Allow
            Principal:
              Service:
                - lambda.amazonaws.com
            Action:
              - sts:AssumeRole
      ManagedPolicyArns:
        - arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole
        - arn:aws:iam::aws:policy/service-role/AWSLambdaENIManagementAccess

  # Main policy, separated from the role so that they
  # can use dependant items without making loops
  LambdaFunctionRolePolicy:
    Type: AWS::IAM::Policy
    Properties:
      PolicyName: MainPolicy
      Roles:
        - !Ref LambdaFunctionRole
      PolicyDocument:
        Version: "2012-10-17"
        Statement:
          - Sid: AccessDynamoDb
            Effect: Allow
            Action:
              - dynamodb:Batch*
              - dynamodb:Condition*
              - dynamodb:DeleteItem
              - dynamodb:Get*
              - dynamodb:Put*
              - dynamodb:Query
              - dynamodb:UpdateItem
              - dynamodb:Scan
            Resource:
              - "*"

  DynamoOptionTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      TableName: event-polling-option
      AttributeDefinitions:
        - AttributeName: pk
          AttributeType: S
        - AttributeName: sk
          AttributeType: S
      KeySchema:
        - AttributeName: pk
          KeyType: HASH
        - AttributeName: sk
          KeyType: RANGE
      BillingMode: PAY_PER_REQUEST
      SSESpecification:
        SSEEnabled: true
      DeletionProtectionEnabled: false

  DynamoVoteTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      TableName: event-polling-vote
      AttributeDefinitions:
        - AttributeName: pk
          AttributeType: S
        - AttributeName: sk
          AttributeType: N
        - AttributeName: email
          AttributeType: S
        - AttributeName: eventId
          AttributeType: S
      KeySchema:
        - AttributeName: pk
          KeyType: HASH
        - AttributeName: sk
          KeyType: RANGE
      BillingMode: PAY_PER_REQUEST
      SSESpecification:
        SSEEnabled: true
      DeletionProtectionEnabled: false
      GlobalSecondaryIndexes:
        - IndexName: GSIEmailEvent
          KeySchema:
            - AttributeName: eventId
              KeyType: HASH
            - AttributeName: email
              KeyType: RANGE
          Projection:
            ProjectionType: KEYS_ONLY

  DynamoCodeTable:
    Type: AWS::DynamoDB::Table
    DeletionPolicy: Retain
    Properties:
      TableName: event-polling-code
      AttributeDefinitions:
        - AttributeName: pk
          AttributeType: S
        - AttributeName: sk
          AttributeType: S
      KeySchema:
        - AttributeName: pk
          KeyType: HASH
        - AttributeName: sk
          KeyType: RANGE
      BillingMode: PAY_PER_REQUEST
      SSESpecification:
        SSEEnabled: true
      DeletionProtectionEnabled: false
  #----------------------------------------------------------------
  # API Gateway processing of events
  #----------------------------------------------------------------
  ApiGateway:
    Type: AWS::Serverless::HttpApi
    Properties:
      Name: !Ref ServiceName
      StageName: live

  # The lambda function that will process Api Gateway requests
  LambdaApiFunction:
    Type: AWS::Serverless::Function
    DependsOn: LambdaFunctionRolePolicy
    Properties:
      FunctionName: !Sub ${ServiceName}-api
      Role: !GetAtt LambdaFunctionRole.Arn
      Runtime: nodejs20.x
      Timeout: 27
      MemorySize: 384
      Tracing: Active
      Layers:
        - !Sub arn:aws:lambda:${AWS::Region}:580247275435:layer:LambdaInsightsExtension:2
      AutoPublishAlias: live
      DeploymentPreference:
        Type: AllAtOnce
      CodeUri: ./build
      Handler: api-handler.handler
      Environment:
        Variables:
          ENV_NAME: !Ref EnvName
          OPTION_TABLE_NAME: !Ref DynamoOptionTable
          VOTE_TABLE_NAME: !Ref DynamoVoteTable
          CODE_TABLE_NAME: !Ref DynamoCodeTable
      Events:
        DefaultEventSource:
          Type: HttpApi
          Properties:
            ApiId: !Ref ApiGateway

  ApiGatewayApiMapping:
    Type: AWS::ApiGatewayV2::ApiMapping
    DependsOn: ApiGatewayliveStage
    Properties:
      DomainName: !ImportValue MAIN-API-DOMAIN-NAME
      ApiId: !Ref ApiGateway
      ApiMappingKey: api/event-polling
      Stage: live

  LogGroup:
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: !Sub /aws/lambda/${ServiceName}-api
