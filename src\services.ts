import { DynamoDB } from "@aws-sdk/client-dynamodb";
import { DynamoDBDocument } from "@aws-sdk/lib-dynamodb";
import { config } from "dotenv";

class ServiceController {
  db: DynamoDBDocument;

  constructor() {
    const isTest = !!process.env.JEST_WORKER_ID;
    this.db = DynamoDBDocument.from(
      isTest
        ? new DynamoDB({
            endpoint: "http://localhost:8000",
            region: "local-env",
            maxAttempts: 1,
            credentials: {
              accessKeyId: "fakeMyKeyId",
              secretAccessKey: "fakeSecretAccessKey",
            },
          })
        : new DynamoDB(),
    );

    // Load environment variables using dotenv
    config();
  }

  get vars() {
    return {
      env: (process.env.ENV_NAME || "prod") as "dev" | "stg" | "prod",
      optionTableName: process.env.OPTION_TABLE_NAME || "",
      voteTableName: process.env.VOTE_TABLE_NAME || "",
      codeTableName: process.env.CODE_TABLE_NAME || "",
    };
  }
}

export const sc = new ServiceController();
