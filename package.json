{"name": "event-polling", "version": "1.0.0", "description": "Service for perfoming polls for events. Originally created for a FILO 2025 event.", "main": "index.js", "scripts": {"build": "tsc", "format": "prettier . --check", "formatfix": "prettier . --write --log-level warn", "lint": "eslint src", "lintfix": "npm run lint -- --fix", "test": "jest --force<PERSON>xit", "testClear": "jest --clear<PERSON>ache", "prepare": "husky"}, "author": "<PERSON><PERSON><PERSON><PERSON>", "license": "ISC", "devDependencies": {"@babel/core": "^7.28.0", "@babel/preset-env": "^7.28.0", "@babel/preset-typescript": "^7.27.1", "@eslint/js": "^9.11.1", "@shelf/jest-dynamodb": "^3.5.0", "@types/jest": "^29.5.13", "@types/koa": "^2.15.0", "@types/koa__router": "^12.0.4", "@types/node-fetch": "^2.6.11", "@types/supertest": "^6.0.3", "babel-jest": "^30.0.5", "cloudformation-js-yaml-schema": "^0.4.2", "eslint": "^9.11.1", "globals": "^15.9.0", "husky": "^9.1.6", "jest": "^30.0.5", "prettier": "3.3.3", "supertest": "^7.0.0", "typescript": "^5.6.2", "typescript-eslint": "^8.7.0"}, "dependencies": {"-": "^0.0.1", "@aws-sdk/client-dynamodb": "^3.848.0", "@aws-sdk/lib-dynamodb": "^3.850.0", "@codegenie/serverless-express": "^4.15.0", "@koa/bodyparser": "^5.1.1", "@koa/router": "^13.1.0", "bcrypt": "^5.1.1", "date-fns": "^4.1.0", "dotenv": "^16.4.5", "koa": "^2.15.3", "node-fetch": "^2.7.0", "zod": "^3.23.8"}}