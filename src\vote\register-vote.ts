import { Middleware } from "@koa/router";
import { sc } from "../services";
import * as z from "zod";
import { CodeEntity, VoteEntity } from "../table-definitions";

export const RegisterVoteRequestSchema = z.object({
  event: z.string(),
  email: z.string(),
  option: z.string(),
  code: z.string(),
});

export const registerVoteMiddleware: Middleware = async ({ request, response }) => {
  try {
    const body = RegisterVoteRequestSchema.parse(request.body);

    //Validates code exists or hasn't been used
    const codeResponse = await sc.db.get({
      TableName: sc.vars.codeTableName,
      Key: {
        pk: body.event,
        sk: body.code,
      },
    });

    if (!codeResponse || !codeResponse.Item) {
      response.status = 400;
      response.body = "Código inválido";
      return;
    }

    const code = codeResponse.Item as CodeEntity;

    if(code.used) {
      response.status = 400;
      response.body = "El código ha sido usado";
      return;
    }

    // Validates the option exists
    const optionResponse = await sc.db.get({
      TableName: sc.vars.optionTableName,
      Key: {
        pk: body.event,
        sk: body.option,
      },
    });

    if (!optionResponse || !optionResponse.Item) {
      response.status = 400;
      response.body = "La opción no existe";
      return;
    }

    // Validates user hasn't voted previously
    const userResponse = await sc.db.get({
      TableName: sc.vars.voteTableName,
      Key: {
        pk: body.event,
        email: body.email,
      },
    });

    if (userResponse  && userResponse.Item) {
      response.status = 400;
      response.body = "El usuario ha votado previamente";
      return;
    }

    // Registers vote
    const voteEntity: VoteEntity = {
      pk: `${body.event}-${body.option}`,
      sk: (new Date()).getTime(),
      email: body.email,
      code: body.code,
    };

    await sc.db.put({
      TableName: sc.vars.voteTableName,
      Item: voteEntity,
    });

  } catch (error) {
    console.log(JSON.stringify(error));
  }
  response.body = null;
};
