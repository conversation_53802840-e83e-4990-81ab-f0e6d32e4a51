import { app } from "../src/api-handler";
import request from "supertest";
import { sc } from "../src/services";
import { OptionEntity } from "../src/table-definitions";

describe("get results tests", () => {
  const testEvent1 = "test-event-123";
  const testEvent2 = "test-event-456";
  const testOption1 = "test-option-1";
  const testOption2 = "test-option-2";
  const testOption3 = "test-option-3";

  beforeEach(async () => {
    // Clean up any existing test data
    await cleanupTestData();
  });

  afterEach(async () => {
    // Clean up test data after each test
    await cleanupTestData();
  });

  const cleanupTestData = async () => {
    try {
      // Clean up option table - scan and delete all test options
      const optionQuery = await sc.db.scan({
        TableName: sc.vars.optionTableName,
      });

      if (optionQuery.Items) {
        for (const item of optionQuery.Items) {
          await sc.db.delete({
            TableName: sc.vars.optionTableName,
            Key: {
              pk: item.pk,
              sk: item.sk,
            },
          });
        }
      }
    } catch (error) {
      // Ignore errors during cleanup
    }
  };

  const setupTestOptions = async () => {
    const options: OptionEntity[] = [
      {
        pk: testEvent1,
        sk: testOption1,
        name: "Option A",
        voteCount: 5,
      },
      {
        pk: testEvent1,
        sk: testOption2,
        name: "Option B",
        voteCount: 3,
      },
      {
        pk: testEvent2,
        sk: testOption3,
        name: "Option C",
        voteCount: 8,
      },
    ];

    for (const option of options) {
      await sc.db.put({
        TableName: sc.vars.optionTableName,
        Item: option,
      });
    }
  };

  test("Successfully get all results when options exist", async () => {
    await setupTestOptions();

    const response = await request(app)
      .get("/live/event-polling/results")
      .query({ eventId: testEvent1 });

    expect(response.statusCode).toEqual(200);
    expect(response.body).toHaveLength(2);

    // Verify the response contains the expected results
    expect(response.body).toEqual(
      expect.arrayContaining([
        expect.objectContaining({
          name: "Option A",
          voteCount: 5,
        }),
        expect.objectContaining({
          name: "Option B",
          voteCount: 3,
        }),
      ]),
    );

    // Verify that only name and voteCount are returned (no pk, sk, etc.)
    response.body.forEach((result: any) => {
      expect(Object.keys(result)).toEqual(["optionId", "name", "voteCount"]);
    });
  });

  test("Return empty array when no options exist", async () => {
    // Don't setup any options

    const response = await request(app)
      .get("/live/event-polling/results")
      .query({ eventId: testEvent1 });

    expect(response.statusCode).toEqual(200);
    expect(response.body).toEqual([]);
  });

  test("Return results with zero vote counts", async () => {
    const optionsWithZeroVotes: OptionEntity[] = [
      {
        pk: testEvent1,
        sk: testOption1,
        name: "Option Zero A",
        voteCount: 0,
      },
      {
        pk: testEvent1,
        sk: testOption2,
        name: "Option Zero B",
        voteCount: 0,
      },
    ];

    for (const option of optionsWithZeroVotes) {
      await sc.db.put({
        TableName: sc.vars.optionTableName,
        Item: option,
      });
    }

    const response = await request(app)
      .get("/live/event-polling/results")
      .query({ eventId: testEvent1 });

    expect(response.statusCode).toEqual(200);
    expect(response.body).toHaveLength(2);

    expect(response.body).toEqual(
      expect.arrayContaining([
        expect.objectContaining({
          name: "Option Zero A",
          voteCount: 0,
        }),
        expect.objectContaining({
          name: "Option Zero B",
          voteCount: 0,
        }),
      ]),
    );
  });

  test("Return results with mixed vote counts", async () => {
    const mixedOptions: OptionEntity[] = [
      {
        pk: testEvent1,
        sk: testOption1,
        name: "High Votes",
        voteCount: 100,
      },
      {
        pk: testEvent1,
        sk: testOption2,
        name: "Medium Votes",
        voteCount: 50,
      },
      {
        pk: testEvent2,
        sk: testOption3,
        name: "Low Votes",
        voteCount: 1,
      },
    ];

    for (const option of mixedOptions) {
      await sc.db.put({
        TableName: sc.vars.optionTableName,
        Item: option,
      });
    }

    const response = await request(app)
      .get("/live/event-polling/results")
      .query({ eventId: testEvent1 });

    expect(response.statusCode).toEqual(200);
    expect(response.body).toHaveLength(2);

    // Verify all different vote counts are returned correctly
    const voteCountsReturned = response.body.map(
      (result: any) => result.voteCount,
    );
    expect(voteCountsReturned).toEqual(expect.arrayContaining([100, 50]));
  });
});
