import { app } from "../src/api-handler";
import request from "supertest";
import { sc } from "../src/services";
import { CodeEntity, OptionEntity } from "../src/table-definitions";

describe("register vote tests", () => {
  const testEvent = "test-event-123";
  const testOption = "test-option-456";
  const testEmail = "<EMAIL>";
  const testCode = "ABC123";

  beforeEach(async () => {
    // Clean up any existing test data
    await cleanupTestData();
  });

  afterEach(async () => {
    // Clean up test data after each test
    await cleanupTestData();
  });

  const cleanupTestData = async () => {
    try {
      // Clean up vote table - scan and delete all votes for test event
      const voteQuery = await sc.db.scan({
        TableName: sc.vars.voteTableName,
      });

      if (voteQuery.Items) {
        for (const item of voteQuery.Items) {
          await sc.db.delete({
            TableName: sc.vars.voteTableName,
            Key: {
              pk: item.pk,
              sk: item.sk,
            },
          });
        }
      }

      // Clean up code table
      await sc.db.delete({
        TableName: sc.vars.codeTableName,
        Key: {
          pk: testEvent,
          sk: testCode,
        },
      });

      // Clean up option table
      await sc.db.delete({
        TableName: sc.vars.optionTableName,
        Key: {
          pk: testEvent,
          sk: testOption,
        },
      });
    } catch (error) {
      // Ignore errors during cleanup
    }
  };

  const setupValidCode = async () => {
    const codeEntity: CodeEntity = {
      pk: testEvent,
      sk: testCode,
      used: false,
    };

    await sc.db.put({
      TableName: sc.vars.codeTableName,
      Item: codeEntity,
    });
  };

  const setupValidOption = async () => {
    const optionEntity: OptionEntity = {
      pk: testEvent,
      sk: testOption,
      name: "Test Option",
      voteCount: 0,
    };

    await sc.db.put({
      TableName: sc.vars.optionTableName,
      Item: optionEntity,
    });
  };

  test("Successfully register a vote with valid data", async () => {
    // Setup test data
    await setupValidCode();
    await setupValidOption();

    const voteRequest = {
      eventId: testEvent,
      email: testEmail,
      optionId: testOption,
      code: testCode,
    };

    const response = await request(app)
      .post("/live/event-polling/")
      .send(voteRequest);

    expect(response.statusCode).toEqual(204);

    // Verify vote was saved in database
    const voteResponse = await sc.db.scan({
      TableName: sc.vars.voteTableName,
    });

    if(!voteResponse || !voteResponse.Items) {
      fail("No vote found in database");
    }

    expect(voteResponse.Items[0]).toEqual(
      expect.objectContaining({
        pk: `${testEvent}#${testOption}`,
        sk: expect.any(Number),
        email: testEmail,
        code: testCode,
      }),
    );
  });
});
